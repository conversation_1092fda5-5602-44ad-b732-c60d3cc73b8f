import pandas as pd
class SmoothedHeikinAshi:
    def __init__(self, 
                 smoothing_period: int = 40,  # M=40 for longer intervals
                 pcy_rho: float = 5.0,
                 pcy_xi: float = 95.0,
                 pcy_delta: float = 8.0):
        self.smoothing_period = smoothing_period
        self.pcy_rho = pcy_rho
        self.pcy_xi = pcy_xi
        self.pcy_delta = pcy_delta
        
    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Smoothed Heikin-Ashi values from OHLC data
        
        Args:
            df: DataFrame with 'open', 'high', 'low', 'close' columns
            
        Returns:
            DataFrame with SHA values added
        """
        # Regular Heikin-Ashi calculation
        ha_close = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        ha_open = (df['open'].shift(1) + df['close'].shift(1)) / 2
        ha_high = df[['high', 'open', 'close']].max(axis=1)
        ha_low = df[['low', 'open', 'close']].min(axis=1)
        
        # Apply smoothing using EMA
        sha_close = ha_close.ewm(span=self.smoothing_period, adjust=False).mean()
        sha_open = ha_open.ewm(span=self.smoothing_period, adjust=False).mean()
        sha_high = ha_high.ewm(span=self.smoothing_period, adjust=False).mean()
        sha_low = ha_low.ewm(span=self.smoothing_period, adjust=False).mean()
        
        # Store SHA values
        df['sha_open'] = sha_open
        df['sha_high'] = sha_high
        df['sha_low'] = sha_low
        df['sha_close'] = sha_close
        
        # Calculate SHA trend
        df['sha_trend'] = np.where(sha_close > sha_open, 1, -1)
        
        return df
    
    def calculate_pcy(self, prices: pd.Series) -> pd.Series:
        """Calculate Price Cyclicality function
        
        Args:
            prices: Series of closing prices
            
        Returns:
            Series of PCY values
        """
        # Calculate price changes
        price_changes = prices.diff()
        
        # Calculate directional changes
        direction_changes = np.sign(price_changes) != np.sign(price_changes.shift(1))
        
        # Calculate PCY using rolling window
        window = self.smoothing_period
        pcy = direction_changes.rolling(window=window, min_periods=1).sum() / window * 100
        
        return pcy
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals with PCY filter"""
        # Calculate basic SHA values
        df = self.calculate(df)
        
        # Calculate PCY
        df['pcy'] = self.calculate_pcy(df['close'])
        
        # Enhanced buy signals with PCY conditions
        df['buy_signal'] = (
            (df['sha_close'] > df['sha_open']) &
            (df['sha_close'] - df['sha_open'] > 
             df['sha_close'].shift(1) - df['sha_open'].shift(1)) &
            (df['pcy'] > self.pcy_rho) &
            (df['pcy'] < self.pcy_xi) &
            (df['pcy'] - df['pcy'].shift(1) > self.pcy_delta)
        )
        
        # Similar conditions for sell signals
        df['sell_signal'] = (
            (df['sha_close'] < df['sha_open']) &
            (df['sha_close'] - df['sha_open'] < 
             df['sha_close'].shift(1) - df['sha_open'].shift(1)) &
            (df['pcy'] > self.pcy_rho) &
            (df['pcy'] < self.pcy_xi) &
            (df['pcy'] - df['pcy'].shift(1) < -self.pcy_delta)
        )
        
        return df