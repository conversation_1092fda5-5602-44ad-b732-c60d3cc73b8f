from zero_lag_indicator import ZeroLagTrendIndicator
import pandas as pd

class LiveDataHandler:
    def __init__(self, strategy, lookback_days=10, fetch_historical_data_func=None):
        self.strategy = strategy
        self.last_historical_update = None
        self.timeframes = {'1': [], '5': [], '15': [], '60': []}
        self.historical_data = {}
        self.lookback_days = lookback_days
        self.current_position = 0
        self.position_entry_price = None
        self.indicator = ZeroLagTrendIndicator(length=70, mult=1.2)
        self.fetch_historical_data = fetch_historical_data_func
        
    def get_latest_signal(self):
        """Get the latest trading signal from the strategy
        
        Returns:
            int: 1 for buy, -1 for sell, 0 for neutral
        """
        if not self.historical_data:
            return 0
            
        # Use the strategy to generate signals
        signals = self.strategy.generate_signals(self.historical_data['15'])  # Using 15-min timeframe
        return signals['signal'].iloc[-1] if not signals.empty else 0
        
    def get_latest_price(self) -> float:
        """Get the latest price from historical data
        
        Returns:
            float: Latest closing price
        """
        if not self.historical_data or '15' not in self.historical_data:
            return 0.0
            
        return self.historical_data['15']['Close'].iloc[-1]
        
    def get_current_position(self) -> int:
        """Get current position size
        
        Returns:
            int: Current position (positive for long, negative for short, 0 for no position)
        """
        return self.current_position
    def __init__(self, lookback_days=10):
        self.last_historical_update = None
        self.timeframes = {'1': [], '5': [], '15': [], '60': []}
        self.historical_data = {}
        self.lookback_days = lookback_days
        self.indicator = ZeroLagTrendIndicator(length=70, mult=1.2)
        
    def initialize_historical_data(self, symbol_data):
        """Initialize with several days of historical data"""
        for tf in self.timeframes:
            # Resample historical data to each timeframe
            resampled = self.indicator.resample_data(symbol_data, tf)
            self.historical_data[tf] = resampled.tail(self.indicator.length * 3)  # Keep 3x indicator length
    
    def needs_historical_update(self) -> bool:
        """Check if we need to refresh historical data"""
        if not self.last_historical_update:
            return True
        
        current_date = pd.Timestamp.now().date()
        days_since_update = (current_date - self.last_historical_update).days
        return days_since_update >= 1
    
    def update_historical_data(self):
        """Update historical data if needed"""
        if self.needs_historical_update():
            if self.fetch_historical_data:
                historical_data = self.fetch_historical_data()
                self.initialize_historical_data(historical_data)
                self.last_historical_update = pd.Timestamp.now().date()
            else:
                print("Warning: fetch_historical_data function not provided")
    
    def update_live_data(self, new_tick):
        """Update both historical and live data"""
        # Check and update historical data if needed
        self.update_historical_data()
        
        # Continue with existing live data update
        for tf in self.timeframes:
            # Convert tick to OHLC format
            tick_data = pd.DataFrame({
                'Open': [new_tick['price']],
                'High': [new_tick['price']],
                'Low': [new_tick['price']],
                'Close': [new_tick['price']],
                'Volume': [new_tick['volume']]
            }, index=[pd.Timestamp(new_tick['timestamp'])])
            
            # Append to historical data
            self.historical_data[tf] = pd.concat([self.historical_data[tf], tick_data])
            
            # Resample to maintain timeframe integrity
            self.historical_data[tf] = self.indicator.resample_data(
                self.historical_data[tf], tf
            ).tail(self.indicator.length * 3)  # Keep limited history
    
    def calculate_live_signals(self):
        """Calculate signals for all timeframes"""
        signals = {}
        for tf in self.timeframes:
            if len(self.historical_data[tf]) >= self.indicator.length:
                df = self.indicator.calculate_signals(self.historical_data[tf])
                signals[tf] = self.indicator.get_latest_signals(df)
        return signals
    
    def is_profitable(self) -> bool:
        """Check if current position is profitable
        
        Returns:
            bool: True if position is profitable, False otherwise
        """
        if self.current_position == 0 or self.position_entry_price is None:
            return False
            
        current_price = self.get_latest_price()
        if current_price == 0.0:
            return False
            
        if self.current_position > 0:  # Long position
            return current_price > self.position_entry_price
        else:  # Short position
            return current_price < self.position_entry_price
