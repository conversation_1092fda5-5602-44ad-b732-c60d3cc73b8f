from combined_strategy import CombinedStrategy
from live_data_handler import LiveDataHandler
from datetime import datetime, time as datetime_time
import time  # Import time module for sleep function
import requests
import os
import json
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
client_id = os.getenv("DHAN_SANDBOX_CLIENT_ID")
access_token = os.getenv("DHAN_SANDBOX_CLIENT_SECRET")

# Initialize strategy
strategy = CombinedStrategy(
    sha_period=10,  # Smoothing period for Heikin-Ashi
    zlema_length=70,  # Zero Lag EMA length
    zlema_mult=1.2  # Zero Lag multiplier
)

# Initialize data handler with the strategy
data_handler = LiveDataHandler(strategy)  # Remove the keyword argument

def fetch_historical_data():
    """Fetch historical data for TATASTEEL"""
    # Use the existing processed data files
    try:
        with open("tatasteel_15m_processed_data.json", "r") as f:
            data = json.load(f)
            
        # Convert to DataFrame
        df = pd.DataFrame(data['data'])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        
        # Rename columns to match expected format
        df = df.rename(columns={
            'open': 'Open', 'high': 'High', 
            'low': 'Low', 'close': 'Close', 
            'volume': 'Volume'
        })
        
        return df
    except Exception as e:
        print(f"Error loading historical data: {e}")
        return pd.DataFrame()

def fetch_current_price():
    """Fetch current price for TATASTEEL"""
    url = "https://sandbox.dhan.co/v2/charts/intraday"
    
    # Request payload
    payload = {
        "securityId": "3499",  # TATASTEEL security ID
        "exchangeSegment": "NSE_EQ",
        "instrument": "EQUITY",
        "interval": "1",  # 1-minute interval for latest price
        "fromDate": datetime.now().strftime("%Y-%m-%d"),
        "toDate": datetime.now().strftime("%Y-%m-%d")
    }

    # Headers
    headers = {
        "access-token": access_token,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        # Make the API request
        response = requests.post(url, json=payload, headers=headers)
        
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            
            # Get the latest close price
            if len(data['close']) > 0:
                return data['close'][-1]
            else:
                print("No price data available")
                return 0.0
        else:
            print(f"API Error: {response.status_code} - {response.text}")
            return 0.0
    except Exception as e:
        print(f"Error fetching current price: {e}")
        return 0.0

def place_buy_order():
    """Place a buy order using Dhan API"""
    print("Placing buy order...")
    url = "https://sandbox.dhan.co/v2/orders"
    
    # Order payload
    payload = {
        "securityId": "3499",  # TATASTEEL security ID
        "exchangeSegment": "NSE_EQ",
        "transactionType": "BUY",
        "quantity": 1,  # Quantity to buy
        "validity": "DAY",
        "orderType": "MARKET",
        "productType": "INTRADAY"
    }
    
    # Headers
    headers = {
        "access-token": access_token,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        # Make the API request
        response = requests.post(url, json=payload, headers=headers)
        
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            print(f"Buy order placed successfully: {data}")
            return True
        else:
            print(f"Order API Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Error placing buy order: {e}")
        return False

def place_sell_order():
    """Place a sell order using Dhan API"""
    print("Placing sell order...")
    url = "https://sandbox.dhan.co/v2/orders"
    
    # Order payload
    payload = {
        "securityId": "3499",  # TATASTEEL security ID
        "exchangeSegment": "NSE_EQ",
        "transactionType": "SELL",
        "quantity": 1,  # Quantity to sell
        "validity": "DAY",
        "orderType": "MARKET",
        "productType": "INTRADAY"
    }
    
    # Headers
    headers = {
        "access-token": access_token,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        # Make the API request
        response = requests.post(url, json=payload, headers=headers)
        
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            print(f"Sell order placed successfully: {data}")
            return True
        else:
            print(f"Order API Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Error placing sell order: {e}")
        return False

def is_trading_allowed(current_time: datetime) -> bool:
    """Check if trading is allowed based on time restrictions
    
    Args:
        current_time: Current datetime
        
    Returns:
        bool: True if trading is allowed, False otherwise
    """
    # Trading hours are typically 9:15 AM to 3:30 PM IST
    trading_start = datetime_time(9, 15)
    trading_end = datetime_time(15, 30)
    grace_period_start = datetime_time(14, 30)  # Last hour is grace period
    
    current_time = current_time.time()
    return trading_start <= current_time < trading_end

def is_grace_period(current_time: datetime) -> bool:
    """Check if current time is in grace period (last trading hour)
    
    Args:
        current_time: Current datetime
        
    Returns:
        bool: True if in grace period, False otherwise
    """
    grace_period_start = datetime_time(14, 30)
    trading_end = datetime_time(15, 30)
    
    current_time = current_time.time()
    return grace_period_start <= current_time < trading_end

# Main trading loop
def run_trading():
    while True:
        current_time = datetime.now()
        
        # Check if trading is allowed
        if not is_trading_allowed(current_time):
            print(f"Trading not allowed at {current_time}")
            time.sleep(60)
            continue
            
        # Create a new tick with current data
        new_tick = {
            'timestamp': current_time,
            'price': fetch_current_price(),  # You need to implement this function
            'volume': 0  # Default volume or fetch from API
        }
            
        # Update market data
        data_handler.update_live_data(new_tick)
        
        # Get latest signals
        current_signal = data_handler.get_latest_signal()
        
        # Get current position if any
        current_position = data_handler.get_current_position()
        
        # Check grace period restrictions
        in_grace_period = is_grace_period(current_time)
        
        # Execute trades based on signals and restrictions
        if current_signal == 1 and not in_grace_period:  # Buy signal
            # Place buy order using Dhan API
            place_buy_order()
        elif current_signal == -1:  # Sell signal
            if in_grace_period and current_position > 0:
                # In grace period, only close existing long positions with profit
                if data_handler.is_profitable():
                    place_sell_order()
            else:
                # Normal trading hours, execute sell signal
                place_sell_order()
                
        # Wait for next update
        time.sleep(60)  # Update every minute


if __name__ == "__main__":
    print("Starting trading system...")
    run_trading()
