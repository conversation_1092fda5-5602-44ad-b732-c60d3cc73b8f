import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import yfinance as yf
from typing import Tuple, Dict, List

class ZeroLagTrendIndicator:
    """
    Zero Lag Trend Signals (MTF) Indicator
    Fixed version with proper timeframe handling and ZLEMA calculation
    """
    
    def __init__(self, length: int = 70, mult: float = 1.2, 
                 bullish_color: str = '#00ffbb', bearish_color: str = '#ff1100'):
        """
        Initialize the Zero Lag Trend Indicator
        
        Args:
            length: Look-back window for Zero-Lag EMA calculations
            mult: Band multiplier (controls thickness of bands)
            bullish_color: Color for bullish signals
            bearish_color: Color for bearish signals
        """
        self.length = length
        self.mult = mult
        self.bullish_color = bullish_color
        self.bearish_color = bearish_color
        
    def ema(self, data: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average with proper initialization"""
        if len(data) == 0:
            return pd.Series([], dtype=float)
        
        # Use the first valid value as the initial value for EMA
        first_valid_idx = data.first_valid_index()
        if first_valid_idx is None:
            return pd.Series([np.nan] * len(data), index=data.index)
            
        # Calculate EMA with proper initial value
        result = data.ewm(span=period, adjust=False).mean()
        return result
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """Calculate Average True Range with proper handling"""
        if len(high) < 2:
            return pd.Series([0] * len(high), index=high.index)
            
        # Calculate True Range components
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        # Use maximum of the three components
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # Calculate ATR using Simple Moving Average (more stable)
        atr_values = true_range.rolling(window=period, min_periods=1).mean()
        return atr_values.fillna(0)
    
    def highest(self, data: pd.Series, period: int) -> pd.Series:
        """Calculate rolling maximum with proper handling"""
        return data.rolling(window=period, min_periods=1).max()
    
    def calculate_zlema(self, close: pd.Series) -> pd.Series:
        """
        Calculate Zero Lag EMA using the correct Ehlers formula
        ZLEMA = EMA(2*Close - Close[lag], length)
        where lag = (length-1)/2
        """
        if len(close) < self.length:
            return close.copy()
            
        # Calculate lag - this is the key correction
        lag = int((self.length - 1) / 2)
        
        # Ensure lag doesn't exceed available data
        lag = min(lag, len(close) - 1)
        
        # Calculate ZLEMA input: 2*Close - Close[lag]
        # This is the correct Ehlers formula
        zlema_input = 2 * close - close.shift(lag)
        
        # Fill NaN values caused by shift with the original close values
        zlema_input = zlema_input.fillna(close)
        
        # Calculate EMA of the adjusted input
        zlema = self.ema(zlema_input, self.length)
        
        return zlema.fillna(close)
    
    def calculate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all indicator signals with improved logic
        """
        df = data.copy()
        
        # Ensure we have enough data
        if len(df) < self.length:
            print(f"Warning: Not enough data for calculations. Need {self.length}, have {len(df)}")
            return df
        
        # Calculate Zero Lag EMA
        df['zlema'] = self.calculate_zlema(df['Close'])
        
        # Calculate volatility (ATR-based bands)
        atr_values = self.atr(df['High'], df['Low'], df['Close'], self.length)
        df['volatility'] = self.highest(atr_values, self.length * 3) * self.mult
        
        # Calculate upper and lower bands
        df['upper_band'] = df['zlema'] + df['volatility']
        df['lower_band'] = df['zlema'] - df['volatility']
        
        # Initialize trend
        df['trend'] = 0
        
        # Calculate trend using proper vectorized logic
        # The key issue was in trend calculation - need to handle persistence correctly
        close_above_upper = df['Close'] > df['upper_band']
        close_below_lower = df['Close'] < df['lower_band']
        
        # Initialize trend array
        trend_values = np.zeros(len(df))
        
        # Calculate trend with proper state persistence
        for i in range(1, len(df)):
            if close_above_upper.iloc[i]:
                trend_values[i] = 1  # Bullish
            elif close_below_lower.iloc[i]:
                trend_values[i] = -1  # Bearish
            else:
                # Maintain previous trend if no clear signal
                trend_values[i] = trend_values[i-1]
        
        df['trend'] = trend_values
        
        # Calculate trend change signals
        df['prev_trend'] = df['trend'].shift(1).fillna(0)
        df['trend_change'] = df['trend'] - df['prev_trend']
        df['bullish_trend_signal'] = (df['trend_change'] > 0) & (df['trend'] == 1)
        df['bearish_trend_signal'] = (df['trend_change'] < 0) & (df['trend'] == -1)
        
        # Calculate entry signals (price crossing ZLEMA in trend direction)
        df['close_cross_zlema_up'] = (df['Close'] > df['zlema']) & (df['Close'].shift(1) <= df['zlema'].shift(1))
        df['close_cross_zlema_down'] = (df['Close'] < df['zlema']) & (df['Close'].shift(1) >= df['zlema'].shift(1))
        
        # Entry signals only when already in trend
        df['bullish_entry'] = (df['close_cross_zlema_up'] & 
                              (df['trend'] == 1) & 
                              (df['prev_trend'] == 1))
        
        df['bearish_entry'] = (df['close_cross_zlema_down'] & 
                              (df['trend'] == -1) & 
                              (df['prev_trend'] == -1))
        
        # Calculate trend status for display
        df['trend_status'] = df['trend'].map({1: 'Bullish', -1: 'Bearish', 0: 'Neutral'})
        
        return df
    
    def resample_data(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        Improved resampling with proper OHLC aggregation and alignment
        """
        # Improved timeframe mapping
        tf_map = {
            '5': '5min',     # 5 minutes
            '15': '15min',   # 15 minutes  
            '60': '1h',      # 1 hour
            '240': '4h',     # 4 hours
            '1D': '1D'       # 1 day
        }
        
        tf = tf_map.get(timeframe, timeframe)
        
        # Ensure data is properly sorted by datetime
        data_sorted = data.sort_index()
        
        # Use proper OHLC resampling with label and closed parameters
        # This is crucial for alignment with TradingView
        try:
            resampled = data_sorted.resample(tf, label='right', closed='right').agg({
                'Open': 'first',
                'High': 'max', 
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum' if 'Volume' in data.columns else 'mean'
            }).dropna()
            
            # Ensure we have valid OHLC data
            resampled = resampled[
                (resampled['Open'].notna()) & 
                (resampled['High'].notna()) & 
                (resampled['Low'].notna()) & 
                (resampled['Close'].notna())
            ]
            
            return resampled
            
        except Exception as e:
            print(f"Error resampling to {tf}: {e}")
            return pd.DataFrame()
    
    def get_multi_timeframe_signals(self, data: pd.DataFrame, 
                                  timeframes: List[str] = ['5', '15', '60', '240', '1D']) -> Dict:
        """
        Calculate signals for multiple timeframes with improved error handling
        """
        mtf_signals = {}
        
        for tf in timeframes:
            try:
                print(f"Processing timeframe: {tf}")
                
                resampled_data = self.resample_data(data, tf)
                
                if len(resampled_data) < self.length:
                    mtf_signals[tf] = {
                        'trend': 0, 
                        'status': f'Insufficient Data ({len(resampled_data)}/{self.length})'
                    }
                    continue
                
                signals = self.calculate_signals(resampled_data)
                
                if len(signals) > 0:
                    latest_trend = signals['trend'].iloc[-1]
                    latest_zlema = signals['zlema'].iloc[-1]
                    latest_close = signals['Close'].iloc[-1]
                    
                    mtf_signals[tf] = {
                        'trend': int(latest_trend),
                        'status': 'Bullish' if latest_trend == 1 else 'Bearish' if latest_trend == -1 else 'Neutral',
                        'close': latest_close,
                        'zlema': latest_zlema,
                        'bars_used': len(signals)
                    }
                else:
                    mtf_signals[tf] = {'trend': 0, 'status': 'No signals calculated'}
                    
            except Exception as e:
                print(f"Error processing {tf}: {e}")
                mtf_signals[tf] = {'trend': 0, 'status': f'Error: {str(e)}'}
        
        return mtf_signals
    
    def plot_indicator(self, data: pd.DataFrame, signals: pd.DataFrame, 
                      title: str = "Zero Lag Trend Signals", figsize: Tuple[int, int] = (15, 10)):
        """
        Plot the indicator with all signals
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, height_ratios=[3, 1], 
                                      sharex=True)
        
        # Main price chart
        ax1.plot(signals.index, signals['Close'], label='Close Price', color='black', linewidth=1)
        ax1.plot(signals.index, signals['zlema'], label='Zero Lag EMA', 
                color=self.bullish_color, linewidth=2, alpha=0.8)
        
        # Plot bands
        bullish_mask = signals['trend'] == 1
        bearish_mask = signals['trend'] == -1
        
        # Plot upper band only when trend is bearish
        upper_band_plot = signals['upper_band'].copy()
        upper_band_plot[bullish_mask] = np.nan
        ax1.plot(signals.index, upper_band_plot, 'r--', alpha=0.6, label='Upper Band')
        
        # Plot lower band only when trend is bullish  
        lower_band_plot = signals['lower_band'].copy()
        lower_band_plot[bearish_mask] = np.nan
        ax1.plot(signals.index, lower_band_plot, 'g--', alpha=0.6, label='Lower Band')
        
        # Fill between bands and zlema
        ax1.fill_between(signals.index, signals['zlema'], signals['upper_band'],
                        where=bearish_mask, color='red', alpha=0.1, interpolate=True)
        ax1.fill_between(signals.index, signals['zlema'], signals['lower_band'],
                        where=bullish_mask, color='green', alpha=0.1, interpolate=True)
        
        # Plot trend change signals
        bullish_trend_points = signals[signals['bullish_trend_signal']]
        bearish_trend_points = signals[signals['bearish_trend_signal']]
        
        ax1.scatter(bullish_trend_points.index, bullish_trend_points['lower_band'], 
                   marker='^', color='green', s=100, label='Bullish Trend', zorder=5)
        ax1.scatter(bearish_trend_points.index, bearish_trend_points['upper_band'], 
                   marker='v', color='red', s=100, label='Bearish Trend', zorder=5)
        
        # Plot entry signals
        bullish_entry_points = signals[signals['bullish_entry']]
        bearish_entry_points = signals[signals['bearish_entry']]
        
        if len(bullish_entry_points) > 0:
            ax1.scatter(bullish_entry_points.index, 
                       bullish_entry_points['zlema'] - bullish_entry_points['volatility'] * 1.5, 
                       marker='^', color='darkgreen', s=50, label='Bullish Entry', zorder=5)
        
        if len(bearish_entry_points) > 0:
            ax1.scatter(bearish_entry_points.index, 
                       bearish_entry_points['zlema'] + bearish_entry_points['volatility'] * 1.5, 
                       marker='v', color='darkred', s=50, label='Bearish Entry', zorder=5)
        
        ax1.set_title(title, fontsize=16, fontweight='bold')
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # Trend subplot
        trend_positive = signals['trend'].copy()
        trend_negative = signals['trend'].copy()
        trend_positive[signals['trend'] <= 0] = 0
        trend_negative[signals['trend'] >= 0] = 0
        
        ax2.fill_between(signals.index, 0, trend_positive, 
                        color='green', alpha=0.7, label='Bullish')
        ax2.fill_between(signals.index, 0, trend_negative, 
                        color='red', alpha=0.7, label='Bearish')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_ylabel('Trend')
        ax2.set_ylim(-1.5, 1.5)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        plt.xlabel('Date')
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def get_latest_signals(self, signals: pd.DataFrame) -> Dict:
        """
        Get the latest signal information
        """
        if len(signals) == 0:
            return {}
            
        latest = signals.iloc[-1]
        
        return {
            'timestamp': signals.index[-1],
            'close_price': latest['Close'],
            'zlema': latest['zlema'],
            'trend': latest['trend'],
            'trend_status': latest['trend_status'],
            'upper_band': latest['upper_band'],
            'lower_band': latest['lower_band'],
            'bullish_entry': latest['bullish_entry'],
            'bearish_entry': latest['bearish_entry'],
            'bullish_trend_signal': latest['bullish_trend_signal'],
            'bearish_trend_signal': latest['bearish_trend_signal']
        }

# Enhanced demo function
def demo_zero_lag_indicator():
    """
    Demonstrate the Fixed Zero Lag Trend Indicator
    """
    print("Fixed Zero Lag Trend Signals Indicator Demo")
    print("=" * 50)
    
    # Initialize indicator
    indicator = ZeroLagTrendIndicator(length=70, mult=1.2)
    
    try:
        # Download sample data with fallback options
        symbol = "TATASTEEL.NS"
        print(f"Downloading data for {symbol}...")
        
        data = None
        
        # Try different data intervals/periods based on Yahoo Finance limitations
        data_options = [
            ("60d", "5m"),   # 60 days, 5-minute intervals
            ("30d", "5m"),   # 30 days, 5-minute intervals  
            ("7d", "5m"),    # 7 days, 5-minute intervals
            ("3mo", "1h"),   # 3 months, 1-hour intervals
            ("6mo", "1h"),   # 6 months, 1-hour intervals
            ("1y", "1d"),    # 1 year, daily intervals
        ]
        
        for period, interval in data_options:
            try:
                print(f"Trying period={period}, interval={interval}...")
                data = yf.download(symbol, period=period, interval=interval)
                
                if not data.empty:
                    print(f"✓ Successfully downloaded {len(data)} bars with {interval} interval")
                    break
                else:
                    print(f"✗ No data returned for {period}/{interval}")
                    
            except Exception as e:
                print(f"✗ Failed {period}/{interval}: {str(e)}")
                continue
        
        # If all downloads failed, create synthetic data
        if data is None or data.empty:
            print("All downloads failed. Creating synthetic data for demo...")
            dates = pd.date_range(start='2024-01-01', periods=2000, freq='5min')
            np.random.seed(42)
            
            # Create more realistic price movement
            returns = np.random.normal(0, 0.001, 2000)  # Small random returns
            trend = np.sin(np.linspace(0, 4*np.pi, 2000)) * 0.002  # Add trend component
            price_changes = returns + trend
            
            price = 100 * np.exp(np.cumsum(price_changes))  # Geometric price evolution
            
            # Add some volatility to OHLC
            volatility = np.random.uniform(0.001, 0.005, 2000)
            
            data = pd.DataFrame({
                'Open': price * (1 + np.random.uniform(-volatility, volatility)),
                'High': price * (1 + np.abs(np.random.uniform(0, volatility * 2))),
                'Low': price * (1 - np.abs(np.random.uniform(0, volatility * 2))),
                'Close': price,
                'Volume': np.random.randint(10000, 100000, 2000)
            }, index=dates)
            
            symbol = "SYNTHETIC_DATA"
        
        # Clean and prepare data
        if not data.empty:
            # Clean column names
            data.columns = [col[0] if isinstance(col, tuple) else col for col in data.columns]
            column_mapping = {
                'Open': 'Open', 'High': 'High', 'Low': 'Low', 'Close': 'Close', 'Volume': 'Volume',
                'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'
            }
            data = data.rename(columns=column_mapping)
            
            # Remove timezone info if present to avoid issues
            if hasattr(data.index, 'tz') and data.index.tz is not None:
                data.index = data.index.tz_localize(None)
            
            # Ensure OHLC consistency
            data['High'] = data[['Open', 'High', 'Low', 'Close']].max(axis=1)
            data['Low'] = data[['Open', 'High', 'Low', 'Close']].min(axis=1)
            
            print(f"Data prepared: {len(data)} bars")
            print(f"Date range: {data.index[0]} to {data.index[-1]}")
            print(f"Price range: ${data['Close'].min():.2f} - ${data['Close'].max():.2f}")
        else:
            raise ValueError("No data available for analysis")
        
        # Calculate signals for base timeframe
        signals = indicator.calculate_signals(data)
        
        # Get latest signals
        latest = indicator.get_latest_signals(signals)
        if latest:
            print(f"\nLatest Signals ({latest['timestamp']}):")
            print(f"Close Price: ${latest['close_price']:.2f}")
            print(f"Zero Lag EMA: ${latest['zlema']:.2f}")
            print(f"Trend Status: {latest['trend_status']}")
            print(f"Upper Band: ${latest['upper_band']:.2f}")
            print(f"Lower Band: ${latest['lower_band']:.2f}")
            
            if latest['bullish_entry']:
                print("🟢 BULLISH ENTRY SIGNAL!")
            if latest['bearish_entry']:
                print("🔴 BEARISH ENTRY SIGNAL!")
            if latest['bullish_trend_signal']:
                print("📈 BULLISH TREND CHANGE!")
            if latest['bearish_trend_signal']:
                print("📉 BEARISH TREND CHANGE!")
        
        # Multi-timeframe analysis with debugging
        print(f"\nMulti-Timeframe Analysis:")
        print("-" * 30)
        mtf_signals = indicator.get_multi_timeframe_signals(data)
        
        for tf, signal in mtf_signals.items():
            status_emoji = "🟢" if signal['status'] == 'Bullish' else "🔴" if signal['status'] == 'Bearish' else "⚪"
            bars_info = f" ({signal.get('bars_used', 'N/A')} bars)" if 'bars_used' in signal else ""
            print(f"{tf:>3} min: {status_emoji} {signal['status']}{bars_info}")
        
        # Calculate and display some statistics
        if len(signals) > 0:
            total_bullish_entries = signals['bullish_entry'].sum()
            total_bearish_entries = signals['bearish_entry'].sum() 
            total_trend_changes = signals['bullish_trend_signal'].sum() + signals['bearish_trend_signal'].sum()
            
            print(f"\nSignal Statistics:")
            print(f"Total Bullish Entries: {total_bullish_entries}")
            print(f"Total Bearish Entries: {total_bearish_entries}")
            print(f"Total Trend Changes: {total_trend_changes}")
            
            # Plot the indicator
            fig = indicator.plot_indicator(data, signals, f"Fixed Zero Lag Trend Signals - {symbol}")
        
        return indicator, signals
        
    except Exception as e:
        print(f"Error in demo: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

# Run the demo
if __name__ == "__main__":
    indicator, signals = demo_zero_lag_indicator()