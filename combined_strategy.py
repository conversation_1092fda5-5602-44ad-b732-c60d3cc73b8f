from zero_lag_indicator import ZeroLagTrendIndicator
from smoothed_heikin_ashi import SmoothedHeikinAshi
import pandas as pd

class CombinedStrategy:
    def __init__(self, 
                 sha_period: int = 10,
                 zlema_length: int = 70,
                 zlema_mult: float = 1.2):
        self.sha = SmoothedHeikinAshi(smoothing_period=sha_period)
        self.zl = ZeroLagTrendIndicator(length=zlema_length, mult=zlema_mult)
        
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals using combined SHA and Zero Lag indicators
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            DataFrame with signals added
        """
        # Calculate SHA values
        df = self.sha.calculate(df)
        
        # Calculate Zero Lag signals
        df = self.zl.calculate_signals(df)
        
        # Generate combined signals
        df['signal'] = 0  # 1 for buy, -1 for sell, 0 for neutral
        
        # Buy signal: SHA trend is up AND Zero Lag shows bullish trend
        buy_condition = (df['sha_trend'] == 1) & (df['trend'] == 1)
        df.loc[buy_condition, 'signal'] = 1
        
        # Sell signal: SHA trend is down AND Zero Lag shows bearish trend
        sell_condition = (df['sha_trend'] == -1) & (df['trend'] == -1)
        df.loc[sell_condition, 'signal'] = -1
        
        return df