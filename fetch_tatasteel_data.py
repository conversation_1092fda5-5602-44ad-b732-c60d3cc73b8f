import requests
import json
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
import os

load_dotenv()
client_id = os.getenv("DHAN_SANDBOX_CLIENT_ID")
access_token = os.getenv("DHAN_SANDBOX_CLIENT_SECRET")
# API endpoint
url = "https://sandbox.dhan.co/v2/charts/intraday"

# Get today's date and yesterday's date in the required format (yyyy-MM-dd)
today = datetime.now().strftime("%Y-%m-%d")
yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

def fetch_tatasteel_data(interval):
    # Request payload
    payload = {
        "securityId": "3499",  # TATASTEEL security ID
        "exchangeSegment": "NSE_EQ",
        "instrument": "EQUITY",
        "interval": interval,  # Use the interval parameter
        "fromDate": today,  # Use today's date instead of future date
        "toDate": today  # Use today's date instead of future date
    }

    # Headers
    headers = {
        "access-token": access_token,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        # Make the API request
        response = requests.post(url, json=payload, headers=headers)
        
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            print(f"Successfully fetched TATASTEEL {interval}-minute candle data!")
            
            # Process and display the data
            print(f"Number of candles: {len(data['timestamp'])}")
            
            # Convert timestamps to readable format and display some sample data
            if len(data['timestamp']) > 0:
                print("\nSample data (first 5 candles):")
                print("Timestamp\t\tOpen\tHigh\tLow\tClose\tVolume")
                
                # Display up to 5 candles or all if less than 5
                for i in range(min(5, len(data['timestamp']))):
                    # Convert timestamp to readable format
                    # Try different timestamp interpretations
                    timestamp = data['timestamp'][i]
                    
                    # Method 1: Interpret as Unix timestamp (seconds since 1970-01-01)
                    candle_time = datetime.fromtimestamp(timestamp)
                    
                    # If the date is still in the future, try alternative methods
                    if candle_time.year > datetime.now().year + 1:
                        # Method 2: Adjust the base date (try with 1970 instead of 1980)
                        base_date = datetime(1970, 1, 1)
                        candle_time = base_date + timedelta(seconds=timestamp)
                        
                        # If still in the future, try one more method
                        if candle_time.year > datetime.now().year + 1:
                            # Method 3: Try milliseconds instead of seconds
                            candle_time = datetime.fromtimestamp(timestamp/1000)
                    
                    print(f"{candle_time}\t{data['open'][i]}\t{data['high'][i]}\t{data['low'][i]}\t{data['close'][i]}\t{data['volume'][i]}")
                
                # Save data to a file
                with open(f"tatasteel_{interval}m_data.json", "w") as f:
                    json.dump(data, f, indent=4)
                print(f"\nData saved to tatasteel_{interval}m_data.json")
                
                # Also save a processed version with readable timestamps
                processed_data = {
                    "data": []
                }
                
                for i in range(len(data['timestamp'])):
                    timestamp = data['timestamp'][i]
                    candle_time = datetime.fromtimestamp(timestamp)
                    
                    # If the date is still in the future, try alternative methods
                    if candle_time.year > datetime.now().year + 1:
                        # Try with 1970 instead of 1980
                        base_date = datetime(1970, 1, 1)
                        candle_time = base_date + timedelta(seconds=timestamp)
                        
                        # If still in the future, try one more method
                        if candle_time.year > datetime.now().year + 1:
                            # Try milliseconds instead of seconds
                            candle_time = datetime.fromtimestamp(timestamp/1000)
                    
                    processed_data['data'].append({
                        "timestamp": candle_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "open": data['open'][i],
                        "high": data['high'][i],
                        "low": data['low'][i],
                        "close": data['close'][i],
                        "volume": data['volume'][i]
                    })
                
                with open(f"tatasteel_{interval}m_processed_data.json", "w") as f:
                    json.dump(processed_data, f, indent=4)
                print(f"Processed data saved to tatasteel_{interval}m_processed_data.json")
            else:
                print("No data available for the specified date range.")
        else:
            print(f"Error: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    interval = input("Enter the minute interval (allowed: 1,5,15,25,60): ")
    print(f"Fetching {interval}-minute candle data for TATASTEEL...")
    fetch_tatasteel_data(interval)